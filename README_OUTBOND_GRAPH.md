# 🚀 Outbond Graph - SDR Research Agent

## Overview

The Outbond Graph is a LangGraph-based multi-agent system designed specifically for Sales Development Representatives (SDRs). It provides intelligent prospect and account research capabilities to help SDRs build highly effective, personalized outreach campaigns.

## 🔑 LangSmith Prompt Dependency

**IMPORTANT**: The Outbond Graph relies entirely on prompts deployed in LangSmith. The system will not function without proper LangSmith configuration and prompt deployment.

### Required LangSmith Setup

1. **LangSmith API Key**: Must be configured in `.env`
2. **Prompt Access**: All prompts must be deployed and accessible in your LangSmith workspace
3. **Network Access**: System requires internet connectivity to pull prompts from LangSmith

### Prompt Inventory

The following prompts must be deployed in your LangSmith workspace:

| Prompt Name | Node | Purpose | Required |
|-------------|------|---------|----------|
| `analyzer_node_sys` | analyzer | Analyzes user queries and understands research intent | ✅ |
| `extract_sub_queries` | plan | Breaks down complex queries into targeted sub-queries | ✅ |
| `generate_alternative_search_queries` | plan | Creates alternative search strategies when initial searches fail | ✅ |
| `summarize_content` | search | Summarizes scraped content relevant to the search query | ✅ |
| `check_answers_in_sources` | analyze | Validates if collected sources answer the research questions | ✅ |
| `generate_streaming_answer` | summarize | Generates comprehensive answers from collected sources | ✅ |
| `generate_follow_up_questions` | summarize | Creates relevant follow-up questions for continued research | ✅ |
| `format_final_answer` | formatter | Formats responses according to user-specified schemas | ✅ |

### Prompt Engineering Process

All prompt optimization was conducted exclusively in LangSmith following these principles:

1. **Iterative Development**: Multiple prompt versions tested and refined
2. **Performance Metrics**: Evaluated on accuracy, helpfulness, and token efficiency
3. **SDR Focus**: Optimized for sales development use cases
4. **Documentation**: All iterations tracked in LangSmith for transparency

## Architecture Overview

### Multi-Agent Design

The system follows a sequential pipeline architecture with specialized nodes:

```
START → analyzer → plan → search → scrape → analyze → summarize → formatter → complete → END
```

Each node has a specific responsibility:
- **analyzer**: Analyzes the user query and understands research intent (uses `analyzer_node_sys`)
- **plan**: Creates targeted search queries based on the analysis (uses `extract_sub_queries`, `generate_alternative_search_queries`)
- **search**: Executes web searches using multiple search engines (uses `summarize_content`)
- **scrape**: Extracts content from relevant web pages and enriches sources with full content
- **analyze**: Processes and validates collected information (uses `check_answers_in_sources`)
- **summarize**: Generates comprehensive research summaries (uses `generate_streaming_answer`, `generate_follow_up_questions`)
- **formatter**: Formats output according to user specifications (uses `format_final_answer`)
- **complete**: Finalizes the response

### Scrape Node Details

The **scrape** node is a critical component that enhances the research pipeline by:

#### Functionality
- **Content Extraction**: Uses LangChain WebBaseLoader for reliable web scraping
- **Content Filtering**: Processes only sources that need additional content (missing or insufficient content)
- **Pass-through Optimization**: Sources with sufficient content bypass scraping for efficiency
- **Rate Limiting**: Implements 150ms delays between requests to respect server limits
- **Timeout Handling**: 15-second timeout with graceful fallback to original sources

#### Processing Pipeline
1. **Source Filtering**: Identifies sources needing content enrichment
2. **Parallel Processing**: Scrapes multiple URLs efficiently
3. **Content Scoring**: Evaluates scraped content quality and relevance
4. **Summarization**: Generates focused summaries using LangSmith prompts
5. **Error Recovery**: Maintains original sources even if scraping fails

#### Technical Implementation
- **HTML to Markdown**: Converts web content to structured markdown format
- **Content Validation**: Ensures minimum content length requirements
- **Quality Assessment**: Scores content relevance to the research query
- **Citation Tracking**: Maintains source attribution throughout the process

### Error Handling

The system includes robust error handling with a dedicated `handleError` node that:
- Captures errors from any stage of the pipeline
- Provides meaningful error messages to users
- Prevents system crashes and maintains user experience
- Logs errors for debugging and monitoring

## Key Features

### 1. Flexible Output Formats
- **Plain Text**: Human-readable summaries for quick consumption
- **Structured JSON**: Machine-readable data for CRM integration and automation
- User-specified schema compliance for JSON outputs

### 2. Citation System
Every response includes comprehensive source citations with:
- Source URLs
- Page titles
- Content snippets
- Access timestamps
- Source reliability indicators

### 3. SDR-Focused Intelligence
The system is optimized for sales research with:
- Company intelligence gathering
- Contact information enrichment
- Industry analysis
- Competitive insights
- Personalization hooks for outreach

### 4. Token Optimization
- Efficient prompt engineering to minimize token usage
- Smart content summarization
- Contextual information filtering
- Optimized search query generation

### 5. Advanced Web Scraping
- **Intelligent Content Extraction**: Focuses on relevant information
- **Multi-format Support**: Handles various website structures
- **Performance Optimization**: Parallel processing with rate limiting
- **Reliability**: Graceful degradation when scraping fails

## State Management

The system uses `SearchState` dataclass for state management:

```python
@dataclass
class SearchState:
    query: str                              # User's research query
    extraction_schema: Optional[dict]       # Output format specification
    context: List[Dict[str, str]]          # Conversation history
    understanding: Optional[str]            # Query analysis
    searchQueries: Optional[List[str]]      # Generated search queries
    sources: List[Source]                   # Initial search results
    scrapedSources: List[Source]           # Enriched sources with full content
    finalAnswer: Optional[str]              # Generated response
    phase: SearchPhase                      # Current processing phase
    error: Optional[str]                    # Error information
```

## Usage Examples

### Plain Text Research
```python
state = SearchState(
    query="Give me a company summary for Stripe",
    extraction_schema={"format": "text"}
)
```

### Structured JSON Output
```python
state = SearchState(
    query="Research contact information for John Doe at Acme Inc",
    extraction_schema={
        "format": "json",
        "fields": {
            "full_name": "string",
            "position": "string", 
            "company": "string",
            "email": "string"
        }
    }
)
```

## Architecture Justification

### Benefits

1. **Efficiency**: Sequential pipeline ensures optimal resource utilization
2. **Scalability**: Stateless nodes enable horizontal scaling
3. **Accuracy**: Multi-stage validation and analysis improve data quality
4. **Token Optimization**: Specialized nodes minimize redundant LLM calls
5. **Maintainability**: Clear separation of concerns simplifies debugging
6. **Content Quality**: Dedicated scraping ensures comprehensive information gathering

### Design Decisions

- **Sequential vs Parallel**: Chosen for data dependency management and cost optimization
- **State-based Communication**: Ensures data consistency across nodes
- **Error Isolation**: Prevents cascading failures in the pipeline
- **Modular Architecture**: Enables independent testing and optimization
- **LangSmith Integration**: Centralized prompt management for easy optimization
- **Scraping Strategy**: Intelligent filtering reduces unnecessary requests

## Scalability Considerations

### Concurrent User Handling
- **Stateless Design**: Each request maintains independent state
- **Resource Pooling**: Shared LLM and search service connections
- **Async Processing**: Non-blocking operations throughout the pipeline

### Potential Bottlenecks & Mitigation

1. **LangSmith API Calls**
   - Mitigation: Prompt caching and connection pooling
   - Fallback: Local prompt fallbacks for critical operations

2. **Search API Rate Limits**
   - Mitigation: Request queuing and rate limiting
   - Multiple search provider fallbacks

3. **Web Scraping Performance**
   - Mitigation: Parallel scraping with connection pooling
   - Content caching for frequently accessed sources
   - Rate limiting to respect server constraints
   - Timeout handling for slow responses

4. **LLM Token Limits**
   - Mitigation: Content chunking and summarization
   - Efficient prompt engineering

5. **Memory Usage**
   - Mitigation: Streaming responses for large datasets
   - State cleanup after completion

### Scaling Strategies

- **Horizontal Scaling**: Deploy multiple graph instances behind load balancer
- **Caching Layer**: Redis for frequently requested research data and scraped content
- **Database Integration**: Persistent storage for research history
- **Monitoring**: LangSmith integration for performance tracking
- **CDN Integration**: Cache static content and common scraping results

## Configuration

The system uses `Configuration` class for runtime settings:
- LLM model selection and parameters
- Search provider configurations
- Scraping timeout and rate limiting settings
- Error handling preferences

### Scraping Configuration

Key scraping parameters in `SEARCH_CONFIG`:
- `MIN_CONTENT_LENGTH`: Minimum content length to trigger scraping
- `MAX_SOURCES_TO_SCRAPE`: Maximum number of sources to scrape per request
- `SCRAPE_TIMEOUT`: Timeout for individual scraping requests
- `SUMMARY_CHAR_LIMIT`: Maximum characters for content summaries

## LangSmith Integration

### Full Instrumentation
- Request/response tracing
- Node execution timing
- Error tracking and analysis
- Performance metrics collection
- Token usage monitoring
- Scraping performance metrics

### Prompt Management
- **Centralized Storage**: All prompts stored in LangSmith
- **Version Control**: Prompt iterations tracked and documented
- **A/B Testing**: Performance comparison between prompt versions
- **Real-time Updates**: Prompt changes deployed without code updates

## Error Scenarios

The system gracefully handles:
- **LangSmith Connectivity Issues**: Fallback mechanisms for prompt retrieval
- **Search API failures**: Alternative search providers
- **Web scraping timeouts**: Graceful degradation with original sources
- **Content extraction errors**: Maintains research flow with available data
- **LLM service interruptions**: Retry logic with exponential backoff
- **Invalid user input**: Clear error messages and guidance
- **Network connectivity issues**: Offline mode capabilities

All errors are logged and reported to users with actionable guidance.

## Performance Metrics

Key performance indicators:
- **Response Time**: Target <30 seconds for complex research
- **Accuracy**: >90% factual accuracy with proper citations
- **Token Efficiency**: <5000 tokens per research request
- **Success Rate**: >95% successful completion rate
- **Scraping Success**: >85% successful content extraction rate
- **Prompt Efficiency**: Optimized through LangSmith evaluation datasets

## Troubleshooting

### LangSmith-Related Issues

1. **Prompt Not Found**: Verify prompt exists in LangSmith workspace
2. **API Key Issues**: Check `LANGCHAIN_API_KEY` in `.env`
3. **Network Connectivity**: Ensure internet access for LangSmith API calls
4. **Prompt Permissions**: Verify workspace access permissions

### Scraping-Related Issues

1. **Scraping Timeouts**: Check network connectivity and target website availability
2. **Content Extraction Failures**: Verify website accessibility and structure
3. **Rate Limiting**: Adjust scraping delays in configuration
4. **Memory Issues**: Monitor content size and implement chunking

### Common Error Messages

- `"Failed to pull prompt from LangSmith"`: Check API key and prompt existence
- `"LangSmith client connection failed"`: Verify network connectivity
- `"Prompt version not found"`: Ensure latest prompt versions are deployed
- `"Scraping timed out for {url}"`: Website took too long to respond
- `"Couldn't access {hostname}"`: Website is unreachable or blocked


