"""State definitions for outbound agent.

State is the interface between the graph and end user as well as the
data model used internally by the graph.
"""
from pydantic import BaseModel
from dataclasses import dataclass, field
from enum import Enum
from typing import Annotated, Any, Dict, List, Optional, TypedDict



class ErrorType(str, Enum):
    """Error types for the outbound agent."""
    SEARCH_ERROR = "search_error"
    SCRAPING_ERROR = "scraping_error"
    LLM_ERROR = "llm_error"
    UKNOWN_ERROR = "unknown"

class SearchPhase(str, Enum):
    """Search phases for the outbound agent."""
    ANALYZING = "analyzing"
    PLANNING = "planning"
    SEARCHING = "searching"
    SCRAPING = "scraping"
    VERIFYING = "verifying"
    SYNTHESIZING = "synthesizing"
    FORMATTER="formatter"
    COMPLETE = "complete"
    ERROR = "error"


# TODO: Figure out best approach to config and file(s) location
class SearchConfig:
    """Search Engine Configuration."""
    
    # Search Settings
    MAX_SEARCH_QUERIES = 1        # Maximum number of search queries to generate
    MAX_SOURCES_PER_SEARCH = 2    # Maximum sources to return per search query
    MAX_SOURCES_TO_SCRAPE = 2     # Maximum sources to scrape for additional content
    # Retry Logic
    MAX_RETRIES = 1               # Maximum retry attempts for failed operations
    MAX_SEARCH_ATTEMPTS = 1       # Maximum attempts to find answers via search
    MIN_ANSWER_CONFIDENCE = 0.3   # Minimum confidence (0-1) that a question was answered
    EARLY_TERMINATION_CONFIDENCE = 0.8  # Confidence level to skip additional searches
   
    # Content Processing
    MIN_CONTENT_LENGTH = 100      # Minimum content length to consider valid
    SUMMARY_CHAR_LIMIT = 100      # Character limit for source summaries
    CONTEXT_PREVIEW_LENGTH = 500  # Preview length for previous context
    ANSWER_CHECK_PREVIEW = 2500   # Content preview length for answer checking
    MAX_SOURCES_TO_CHECK = 10     # Maximum sources to check for answers
   
    # Timeouts
    SCRAPE_TIMEOUT = 15000        # Timeout for scraping operations (ms)
   
    # Performance
    PARALLEL_SUMMARY_GENERATION = True  # Generate summaries in parallel


SEARCH_CONFIG = SearchConfig()



@dataclass(kw_only=True)
class Source:
    """Represents a source with metadata."""
    url: str
    """The source URL."""
    title: str
    """The title of the source."""
    content: Optional[str] = None
    """The content of the source."""
    quality: Optional[float] = None
    """Quality score of the source."""
    summary: Optional[str] = None
    """Summary of the source content."""

class SubQuery(TypedDict):
    """Represents a sub-query with its metadata."""
    question: str
    """The sub-query question."""
    searchQuery: str
    """The search query used to find the source."""
    answered: bool
    """Whether the sub-query has been answered."""
    answer: Optional[str]
    """The answer to the sub-query."""
    confidence: float
    """Confidence score of the answer."""
    sources: List[str]
    """List of source URLs used to answer the sub-query."""


# Reducers 

# SearchPhase
def take_latest_search_phase(x: SearchPhase, y: SearchPhase) -> SearchPhase:
    """Reducer that takes the latest search phase value."""
    return y if y is not None else x
def deduplicate_sources_reducer(existing: List[Source], update: Optional[List[Source]]) -> List[Source]:
    """Reducer that deduplicates sources by URL."""
    if not update:
        return existing
    # Deduplicate sources by URL
    source_map = {}
    for source in existing + update:
        source_map[source.url] = source
    return list(source_map.values())



def take_latest_optional_source_list(x: Optional[List[Source]], y: Optional[List[Source]]) -> Optional[List[Source]]:
    """Reducer that takes the latest optional source list value."""
    return y if y is not None else x

def concatenate_sources_reducer(existing: List[Source], update: Optional[List[Source]]) -> List[Source]:
    """Reducer that concatenates source lists."""
    if not update:
        return existing
    return existing + update

def take_latest_int(x: int, y: int) -> int:
    """Reducer that takes the latest integer value."""
    return y if y is not None else x

def take_latest_string(x: str, y: str) -> str:
    """Reducer that takes the latest string value."""
    return y if y is not None else x

def take_latest_optional_string(x: Optional[str], y: Optional[str]) -> Optional[str]:
    """Reducer that takes the latest optional string value."""
    return y if y is not None else x


def take_latest_optional_error_type(x: Optional[ErrorType], y: Optional[ErrorType]) -> Optional[ErrorType]:
    """Reducer that takes the latest optional error type value."""
    return y if y is not None else x




def take_latest_list(x: List[Any], y: List[Any]) -> List[Any]:
    """Reducer that takes the latest list value.""" 
    return y if y is not None else x

def take_latest_optional_string_list(x: Optional[List[str]], y: Optional[List[str]]) -> Optional[List[str]]:
    """Reducer that takes the latest optional string list value."""
    return y if y is not None else x
def take_latest_optional_subquery_list(x: Optional[List[SubQuery]], y: Optional[List[SubQuery]]) -> Optional[List[SubQuery]]:
    """Reducer that takes the latest optional sub-query list value."""
    return y if y is not None else x



def take_latest_optional_dict(x: Optional[dict[str, Any]], y: Optional[dict[str, Any]]) -> Optional[dict[str, Any]]:
    """Reducer that takes the latest optional dictionary value."""
    return y if y is not None else x


@dataclass(kw_only=True)
class SearchState:
    """Search state for outbound agent operations.
    
    This state manages search queries and context for the outbound agent,
    equivalent to the TypeScript SearchStateAnnotation.
    """
    
    # Input fields
    query: Annotated[str, take_latest_string] = field(default=None)
    """The search query string."""
    
    extraction_schema: Annotated[Optional[dict[str, Any]], take_latest_optional_dict] = field(default=None)
    "The simple extraction schema in format: {'format': 'json', 'fields': {'field_name': 'data_type'}} or {'format': 'text'}."

    
    context: Annotated[List[Dict[str, str]], take_latest_list] = field(default_factory=list)
    """List of context objects containing query and response pairs.
    
    Each context item is a dictionary with:
    - query: str - The query that was made
    - response: str - The response received
    """
    
    # Process fields
    understanding: Annotated[Optional[str], take_latest_optional_string] = field(default=None)
    """The current understanding of the search context."""
    
    searchQueries: Annotated[Optional[List[str]], take_latest_optional_string_list] = field(default=None)
    """List of search queries to be executed."""
    
    currentSearchIndex: Annotated[int, take_latest_int] = field(default=0)
    """Index of the current search query being processed."""
    
    # Results fields - with proper array reducers
    sources: Annotated[List[Source], deduplicate_sources_reducer] = field(default_factory=list)
    """List of sources with deduplication by URL."""
    
    scrapedSources: Annotated[List[Source], concatenate_sources_reducer] = field(default_factory=list)
    """List of scraped sources (concatenated without deduplication)."""
    
    processedSources: Annotated[Optional[List[Source]], take_latest_optional_source_list] = field(default=None)
    """List of processed sources."""
    
    finalAnswer: Annotated[Optional[str], take_latest_optional_string] = field(default=None)
    """The final answer to the query."""
    
    followUpQuestions: Annotated[Optional[List[str]], take_latest_optional_string_list] = field(default=None)
    """List of follow-up questions."""
    
    # Answer tracking
    subQueries: Annotated[Optional[List[SubQuery]], take_latest_optional_subquery_list] = field(default=None)
    """List of sub-queries with their metadata and answers."""
    
    searchAttempt: Annotated[int, take_latest_int] = field(default=0)
    """Number of search attempts made."""
    
    # Control fields
    phase: Annotated[SearchPhase, take_latest_search_phase] = field(default=SearchPhase.ANALYZING)
    """Current phase of the search process."""
    
    error: Annotated[Optional[str], take_latest_optional_string] = field(default=None)
    """Error message if an error occurred."""
    
    errorType: Annotated[Optional[ErrorType], take_latest_optional_error_type] = field(default=None)
    """Type of error that occurred."""
    
    maxRetries: Annotated[int, take_latest_int] = field(default=SEARCH_CONFIG.MAX_RETRIES)
    """Maximum number of retry attempts allowed."""
    
    retryCount: Annotated[int, take_latest_int] = field(default=0)
    """Current number of retry attempts made."""
