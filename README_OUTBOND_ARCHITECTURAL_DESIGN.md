# Outbond Graph Documentation

## Overview

The Outbond Graph is a sequential pipeline-based LangGraph system that relies entirely on prompts deployed in LangSmith for SDR research workflows.

## Architecture

### Sequential Pipeline Design

The graph follows a linear progression through specialized nodes:

**Reference**: `src/outbond/graph.py`

```
START → analyzer → plan → search → scrape → analyze → summarize → formatter → complete → END
```

### State Management

Uses `SearchState` with phase-based progression tracking:

**Reference**: `src/outbond/state.py`

```python
@dataclass
class SearchState:
    query: str = ""
    phase: SearchPhase = SearchPhase.ANALYZING
    sources: Optional[List[Source]] = None
    scrapedSources: Optional[List[Source]] = None
    processedSources: Optional[List[Source]] = None
    finalAnswer: Optional[str] = None
    followUpQuestions: Optional[List[str]] = None
```

## Critical LangSmith Dependencies

**⚠️ IMPORTANT**: This graph requires 8 specific prompts deployed in LangSmith and will not function without them.

### Required Prompts

**Reference**: `README_OUTBOND_GRAPH.md`

- `analyzer_node_sys` - Query analysis
- `extract_sub_queries` - Query decomposition  
- `generate_alternative_search_queries` - Search strategy generation
- `summarize_content` - Content summarization
- `check_answers_in_sources` - Source validation
- `generate_streaming_answer` - Answer generation
- `generate_follow_up_questions` - Follow-up generation
- `format_final_answer` - Response formatting

### LangSmith Integration Implementation

**Reference**: `src/outbond/nodes/summarize.py`

```python
LANGSMITH_API_KEY = os.getenv('LANGSMITH_API_KEY')
client = AsyncClient(api_key=LANGSMITH_API_KEY)
prompt = await client.pull_prompt("generate_streaming_answer", include_model=False)
```

## Node Implementations

### Search Node
**Reference**: `src/outbond/nodes/search.py`

- Executes web searches using Firecrawl
- Transforms results to Source objects
- Applies search limits via `SEARCH_CONFIG.MAX_SOURCES_PER_SEARCH`

### Scrape Node
**Reference**: `src/outbond/nodes/scrape.py`

- Extracts detailed content from sources
- Creates enriched sources with scraped content
- Includes rate limiting with 150ms delays
- Generates content summaries using LangSmith prompts

```python
# Add small delay for rate limiting
await asyncio.sleep(0.15)  # 150ms delay
```

### Analyze Node
**Reference**: `src/outbond/nodes/analize.py`

- Combines sources and removes duplicates by URL
- Uses source mapping to prevent duplicate processing

## Error Handling

**Reference**: `src/outbond/graph.py`

Includes dedicated `handleError` node with conditional routing:

```python
workflow.add_conditional_edges(
    "handleError",
    lambda state: END if state.phase == SearchPhase.ERROR else "analyzer"
)
```

## Configuration

**Reference**: `langgraph.json`

```json
{
  "dependencies": ["."],
  "graphs": {
     "outbound": "./src/outbond/graph.py:graph"
  },
  "env": ".env"
}
```

## Simplified Version

**Reference**: `src/outbond/graph_simple.py`

A simplified 3-node version exists for testing:
```
START → analyzer → plan → complete → END
```

---
