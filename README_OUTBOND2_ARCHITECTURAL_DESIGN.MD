
# Outbond2 Graph Documentation

## Overview

The Outbond2 Graph is a self-contained, orchestrator-based LangGraph system with embedded prompts, designed specifically for SDR research workflows with quality validation.

## Architecture

### Orchestrator Pattern Design

**Reference**: `src/outbond2/graph.py`

```python
workflow = StateGraph(State, config_schema=Configuration)
workflow.add_node(sdr_research_orchestrator)
workflow.add_node(sdr_quality_validator)
workflow.add_node("tools", create_tools_node())
```

**Flow**: `START → sdr_research_orchestrator → tools → sdr_research_orchestrator → sdr_quality_validator → END`

### State Management

**Reference**: `src/outbond2/state.py`

Enhanced state with citation tracking and loop control:

```python
@dataclass(kw_only=True)
class State(InputState):
    messages: Annotated[List[BaseMessage], add_messages] = field(default_factory=list)
    loop_step: Annotated[int, operator.add] = field(default=0)
    citations: Annotated[List[Citation], lambda x, y: x + y] = field(default_factory=list)
```

## Core Components

### 1. SDR Research Orchestrator

**Reference**: `src/outbond2/nodes/sdr_research_orchestrator.py`

- Main decision-making component
- Coordinates between data gathering and output generation
- Manages tool calls and research flow
- Handles citation integration

### 2. SDR Quality Validator

**Reference**: `src/outbond2/nodes/sdr_quality_validator.py`

- Validates research completeness for SDR use cases
- Uses `InfoIsSatisfactory` model for structured validation
- Provides improvement instructions when research is insufficient

```python
class InfoIsSatisfactory(BaseModel):
    reason: list[str] = Field(description="...")
    is_satisfactory: bool = Field(description="...")
    improvement_instructions: Optional[str] = Field(description="...")
```

### 3. Tools Node

**Reference**: `src/outbond2/nodes/tools_node.py`

```python
def create_tools_node() -> ToolNode:
    return ToolNode([search, scrape_website])
```

### 4. Routing Logic

**Reference**: `src/outbond2/nodes/routes.py`

Smart routing based on tool calls:

```python
def route_after_agent(state: State) -> str:
    last_message = state.messages[-1]
    
    if last_message.tool_calls and last_message.tool_calls[0]["name"] == "Info":
        return "sdr_quality_validator"
    else:
        return "tools"
```

## Citation System

### Citation Structure

**Reference**: `src/outbond2/state.py`

```python
@dataclass
class Citation:
    url: str
    title: str
    content_snippet: str
    source_type: str  # "search" or "scrape"
```

### Citation Utilities

**Reference**: `src/outbond2/utils.py`

- `deduplicate_citations()` - Removes duplicate citations
- `format_citations_for_text()` - Formats for human-readable output
- `format_citations_for_json()` - Formats for structured output
- `add_citations_to_response()` - Integrates citations into responses

## Tools Implementation

### Available Tools

**Reference**: `src/outbond2/tools/tools.py`

- `search` - Web search with automatic citation creation
- `scrape_website` - Website content extraction with citation tracking

## Configuration

**Reference**: `src/outbond2/configuration.py`

Self-contained configuration with embedded prompts:

```python
@dataclass
class Configuration:
    model: str = "anthropic/claude-3-5-sonnet-20240620"
    prompt: str = MAIN_PROMPT  # From prompts.py
    max_search_results: int = 10
    max_info_tool_calls: int = 3
    max_loops: int = 6
```

### Embedded Prompts

**Reference**: `src/outbond2/prompts.py`

```python
MAIN_PROMPT = """You are an expert Sales Development Representative (SDR) research assistant..."""
```

## Schema Support

**Reference**: `README_INPUT_SCHEMA.md`

Supports flexible schema formats:

- JSON format with structured fields
- Text format for unstructured responses
- Schema validation and conversion utilities

## Node Package Structure

**Reference**: `src/outbond2/nodes/__init__.py`

```python
__all__ = [
    "sdr_research_orchestrator",
    "sdr_quality_validator", 
    "create_tools_node",
    "route_after_agent",
    "route_after_checker",
]
```

## LLM Integration

**Reference**: `src/shared/llm_factory.py`

Uses OpenRouter for model access:

```python
def create_llm(model_name: str = "google/gemini-2.5-flash", **kwargs):
    return ChatOpenRouterAI(model=model_name, **kwargs)
```

## Key Differences from Outbond Graph

1. **No External Dependencies**: Uses embedded prompts instead of LangSmith
2. **Citation Tracking**: Built-in citation system throughout workflow
3. **Quality Validation**: Dedicated validation node with structured feedback
4. **Orchestrator Pattern**: More flexible than sequential pipeline
5. **Loop Control**: Built-in loop management with `max_loops` configuration
