# Input Schema Guide for Outbond Graph

## Overview

The SDR Enrichment Agent uses a flexible schema system to define the structure of extracted information. This guide explains how to properly format your extraction schemas for both JSON and text outputs.

## Schema Format Types

### 1. JSON Format Schema

For structured data extraction, use the JSON format:

```json
{
  "format": "json",
  "fields": {
    "field_name": "data_type",
    "another_field": "data_type"
  }
}
```

### 2. Text Format Schema

For unstructured text responses, use the text format:

```json
{
  "format": "text"
}
```

## Valid Data Types

When using JSON format, you can specify these data types:

| Data Type | Description | Example Values |
|-----------|-------------|----------------|
| `"string"` | Text data | `"Acme Corp"`, `"Software"` |
| `"integer"` or `"int"` | Whole numbers | `100`, `500` |
| `"number"` or `"float"` | Decimal numbers | `3.14`, `99.9` |
| `"boolean"` or `"bool"` | True/false values | `true`, `false` |
| `"array"` | List of items | `["item1", "item2"]` |
| `"object"` | Nested object | `{"key": "value"}` |

## Nested Array Schemas

You can now define structured array items with specific field definitions:

### Simple Array (Existing)
```json
{
  "format": "json",
  "fields": {
    "company_names": "array",
    "technologies": "array"
  }
}
```
*Result*: Arrays with unstructured items

### Nested Array with Structured Items (New)
```json
{
  "format": "json",
  "fields": {
    "companies": {
      "type": "array",
      "items": {
        "company_name": "string",
        "industry": "string",
        "hq_location": "string",
        "short_description": "string"
      }
    }
  }
}
```
*Result*: Array of objects with guaranteed field structure

### Mixed Schema Support
```json
{
  "format": "json",
  "fields": {
    "research_title": "string",
    "companies": {
      "type": "array",
      "items": {
        "name": "string",
        "employees": "integer",
        "founded": "integer",
        "website": "string"
      }
    },
    "total_companies_found": "integer",
    "research_keywords": "array"
  }
}
```

### Nested Array Benefits for SDR Use Cases

1. **Guaranteed Field Structure**: Each array item will have the exact fields you specify
2. **Consistent Data Format**: Perfect for CRM imports and data processing
3. **Null Handling**: Missing data is properly marked as `null` instead of omitted
4. **Type Safety**: Each field maintains its specified data type

### Nested Array Examples

**Company Directory**:
```json
{
  "format": "json",
  "fields": {
    "companies": {
      "type": "array",
      "items": {
        "company_name": "string",
        "industry": "string",
        "hq_location": "string",
        "employee_count": "integer",
        "website": "string",
        "short_description": "string"
      }
    }
  }
}
```

**Contact List**:
```json
{
  "format": "json",
  "fields": {
    "contacts": {
      "type": "array",
      "items": {
        "full_name": "string",
        "job_title": "string",
        "company": "string",
        "linkedin_url": "string",
        "email_domain": "string"
      }
    }
  }
}
```

**Technology Stack Analysis**:
```json
{
  "format": "json",
  "fields": {
    "technologies": {
      "type": "array",
      "items": {
        "technology_name": "string",
        "category": "string",
        "adoption_level": "string",
        "vendor": "string",
        "implementation_complexity": "string"
      }
    }
  }
}
```

**Competitor Analysis**:
```json
{
  "format": "json",
  "fields": {
    "competitors": {
      "type": "array",
      "items": {
        "company_name": "string",
        "market_position": "string",
        "strengths": "string",
        "weaknesses": "string",
        "market_share": "string",
        "pricing_strategy": "string"
      }
    }
  }
}
```

## SDR Use Case Examples

### Company Research Schema

```json
{
  "format": "json",
  "fields": {
    "company_name": "string",
    "industry": "string",
    "employee_count": "integer",
    "annual_revenue": "string",
    "headquarters_location": "string",
    "founded_year": "integer",
    "is_public": "boolean",
    "recent_news": "array",
    "key_executives": "array",
    "technology_stack": "array",
    "funding_rounds": "array",
    "competitors": "array"
  }
}
```

### Contact Enrichment Schema

```json
{
  "format": "json",
  "fields": {
    "full_name": "string",
    "job_title": "string",
    "department": "string",
    "company_name": "string",
    "linkedin_url": "string",
    "email_pattern": "string",
    "years_in_role": "integer",
    "previous_companies": "array",
    "education": "array",
    "recent_activities": "array"
  }
}
```

### Lead Qualification Schema

```json
{
  "format": "json",
  "fields": {
    "company_name": "string",
    "lead_score": "integer",
    "pain_points": "array",
    "budget_indicators": "string",
    "decision_makers": "array",
    "buying_signals": "array",
    "competitive_landscape": "array",
    "implementation_timeline": "string",
    "technology_needs": "array"
  }
}
```

### Market Intelligence Schema

```json
{
  "format": "json",
  "fields": {
    "industry_name": "string",
    "market_size": "string",
    "growth_rate": "number",
    "key_trends": "array",
    "major_players": "array",
    "regulatory_changes": "array",
    "technology_disruptions": "array",
    "market_challenges": "array",
    "opportunities": "array"
  }
}
```

### Simple List Extraction

```json
{
  "format": "json",
  "fields": {
    "company_names": "array",
    "contact_emails": "array",
    "phone_numbers": "array"
  }
}
```

### Text-Based Analysis

```json
{
  "format": "text"
}
```

Use this for:
- Executive summaries
- Detailed company profiles
- Market analysis reports
- Competitive intelligence briefs

## Common SDR Research Patterns

### 1. Account Research

```json
{
  "format": "json",
  "fields": {
    "company_overview": "string",
    "key_business_challenges": "array",
    "recent_milestones": "array",
    "growth_indicators": "array",
    "decision_makers": "array",
    "technology_initiatives": "array",
    "competitive_threats": "array",
    "outreach_opportunities": "array"
  }
}
```

### 2. Prospect Profiling

```json
{
  "format": "json",
  "fields": {
    "prospect_name": "string",
    "current_role": "string",
    "career_progression": "array",
    "areas_of_responsibility": "array",
    "professional_interests": "array",
    "social_media_activity": "array",
    "mutual_connections": "array",
    "personalization_hooks": "array"
  }
}
```

### 3. Industry Analysis

```json
{
  "format": "json",
  "fields": {
    "industry_overview": "string",
    "current_trends": "array",
    "regulatory_environment": "string",
    "technology_adoption": "array",
    "vendor_landscape": "array",
    "buyer_personas": "array",
    "sales_cycles": "string",
    "common_objections": "array"
  }
}
```

## Best Practices

### 1. Field Naming
- Use descriptive, snake_case field names
- Be specific: `"job_title"` not `"title"`
- Use consistent naming across schemas

### 2. Data Type Selection
- Use `"array"` for lists (competitors, technologies, etc.)
- Use `"integer"` for counts and years
- Use `"string"` for most text data
- Use `"boolean"` for yes/no questions

### 3. Schema Design Tips
- Include fields that directly support sales activities
- Think about how the data will be used in outreach
- Balance detail with practicality
- Consider the time required to extract each field

### 4. SDR-Specific Considerations
- Include fields for personalization opportunities
- Capture pain points and business challenges
- Track recent events and triggers
- Identify decision makers and influencers

## Error Handling

### Common Schema Errors

❌ **Incorrect**: Missing quotes
```json
{
  format: "json",
  fields: {
    company_name: "string"
  }
}
```

✅ **Correct**: Proper JSON format
```json
{
  "format": "json",
  "fields": {
    "company_name": "string"
  }
}
```

❌ **Incorrect**: Invalid array syntax
```json
{
  "format": "json",
  "fields": {
    "companies": [...]
  }
}
```

✅ **Correct**: Use "array" data type
```json
{
  "format": "json",
  "fields": {
    "companies": "array"
  }
}
```

### Validation

The system will validate your schema and provide helpful error messages for:
- Invalid JSON syntax
- Missing required fields (`"format"`)
- Invalid data types
- Malformed structure

## Advanced Usage

### Complex Nested Data

For complex data structures, use `"object"` type and let the AI structure the data appropriately:

```json
{
  "format": "json",
  "fields": {
    "company_profile": "object",
    "financial_metrics": "object",
    "contact_information": "object"
  }
}
```

### Dynamic Field Selection

You can adjust your schema based on research goals:

**Quick Research** (3-5 fields):
```json
{
  "format": "json",
  "fields": {
    "company_name": "string",
    "industry": "string",
    "employee_count": "integer"
  }
}
```

**Deep Research** (10+ fields):
```json
{
  "format": "json",
  "fields": {
    "company_name": "string",
    "industry": "string",
    "employee_count": "integer",
    "annual_revenue": "string",
    "key_executives": "array",
    "recent_news": "array",
    "technology_stack": "array",
    "competitors": "array",
    "funding_history": "array",
    "growth_indicators": "array"
  }
}
```

## Integration with Tools

The schema works seamlessly with all available research tools:

- **search_company_news**: Populates news and announcement fields
- **search_company_financials**: Fills financial and growth data
- **search_linkedin_company**: Provides employee and culture information
- **search_person_linkedin**: Extracts contact and professional details
- **search_company_jobs**: Identifies hiring patterns and growth signals

The AI automatically selects appropriate tools based on your schema requirements and optimizes the research process for maximum efficiency and accuracy. 